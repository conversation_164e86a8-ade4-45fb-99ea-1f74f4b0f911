/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable React strict mode to reduce hydration warnings
  reactStrictMode: false,

  // Enable experimental auth interrupts for better auth performance
  experimental: {
    authInterrupts: true,
  },

  // Suppress specific console warnings in development
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 2,
  },

  // Configure compiler options
  compiler: {
    // Remove console.* output except errors in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error'],
    } : false,
  },
};

module.exports = nextConfig;

