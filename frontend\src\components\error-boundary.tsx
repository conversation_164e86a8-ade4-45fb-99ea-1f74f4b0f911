"use client";

import { Component, ErrorInfo, ReactNode } from "react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * Custom error boundary component that can be used to catch and handle errors
 * in the component tree. It also suppresses hydration warnings.
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // If the error is a hydration error, we don't want to show the error UI
    if (
      error.message.includes("Hydration failed") ||
      error.message.includes("Text content did not match") ||
      error.message.includes("Expected server HTML to contain") ||
      error.message.includes("did not match") ||
      error.message.includes("A tree hydrated but some attributes") ||
      error.message.includes("rp-extension")
    ) {
      // Just log the error but don't update state to trigger error UI
      console.warn("Suppressed hydration error:", error);
      return { hasError: false, error: null };
    }

    // For other errors, update state to trigger error UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError && this.props.fallback) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
