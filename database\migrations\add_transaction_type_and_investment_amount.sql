-- Add transaction_type column to portfolio_entries table
ALTER TABLE dse_schema.portfolio_entries 
ADD COLUMN transaction_type VARCHAR(10) NOT NULL DEFAULT 'buy' 
CHECK (transaction_type IN ('buy', 'sell'));

-- Rename buy_price to transaction_price for clarity
ALTER TABLE dse_schema.portfolio_entries 
RENAME COLUMN buy_price TO transaction_price;

-- Rename buy_date to transaction_date for clarity
ALTER TABLE dse_schema.portfolio_entries 
RENAME COLUMN buy_date TO transaction_date;

-- Convert transaction_date to timestamp with time zone
ALTER TABLE dse_schema.portfolio_entries 
ALTER COLUMN transaction_date TYPE TIMESTAMP WITH TIME ZONE 
USING transaction_date::timestamp with time zone;

-- Add investment_amount column to portfolios table
ALTER TABLE dse_schema.portfolios 
ADD COLUMN investment_amount FLOAT DEFAULT 0.0;

-- Update existing entries to have transaction_type = 'buy'
UPDATE dse_schema.portfolio_entries 
SET transaction_type = 'buy';

-- Add comment to explain the changes
COMMENT ON COLUMN dse_schema.portfolio_entries.transaction_type IS 'Type of transaction: buy or sell';
COMMENT ON COLUMN dse_schema.portfolio_entries.transaction_price IS 'Price per unit for this transaction';
COMMENT ON COLUMN dse_schema.portfolio_entries.transaction_date IS 'Date of the transaction';
COMMENT ON COLUMN dse_schema.portfolios.investment_amount IS 'Total investment amount in BDT';
