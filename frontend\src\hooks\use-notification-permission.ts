"use client";

import { useCallback, useEffect, useState } from "react";

type PermissionStatus = "default" | "granted" | "denied";

export function useNotificationPermission() {
  const [permission, setPermission] = useState<PermissionStatus>("default");

  // On component mount, check the current notification permission status.
  useEffect(() => {
    // Ensure this code runs only in the browser.
    if (typeof window !== "undefined" && "Notification" in window) {
      setPermission(Notification.permission);
    }
  }, []);

  // A function to request permission from the user.
  const requestPermission = useCallback(async () => {
    // Check if the Notification API is available in the browser.
    if (!("Notification" in window)) {
      console.error("This browser does not support desktop notification");
      return;
    }

    // `requestPermission` returns a promise that resolves to the new permission status.
    const status = await Notification.requestPermission();
    setPermission(status);
  }, []);

  return { permission, requestPermission };
}
