# This file sets up relationships between models after all models are loaded
# to avoid circular import issues

from sqlalchemy.orm import relationship
from app.models.user import User
from app.models.portfolio import Portfolio

# Add portfolios relationship to User
User.portfolios = relationship(
    "Portfolio",
    primaryjoin="User.id == Portfolio.user_id",
    cascade="all, delete-orphan",
    lazy="selectin"
)
