import requests
from app.api.deps import get_current_active_admin, get_db
from app.models.trading_code import TradingCode
from bs4 import BeautifulSoup
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import text as sql_text
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

@router.post("/sync-trading-codes", status_code=status.HTTP_200_OK)
async def sync_trading_codes(
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(get_current_active_admin),
):
    """
    Sync trading codes from DSE website
    """
    # Fetch HTML content
    url = "https://stockanalysis.com/list/dhaka-stock-exchange/"
    response = requests.get(url)

    if response.status_code != 200:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch data from DSE website"
        )

    # Parse HTML
    soup = BeautifulSoup(response.content, 'html.parser')
    table = soup.find('table')

    if not table:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not find trading codes table on DSE website"
        )

    # Find header row to identify column positions
    headers = table.find('tr')
    if not headers:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not find table headers"
        )

    # Get column indices
    header_cells = headers.find_all('th')
    symbol_idx = None
    company_name_idx = None

    for i, cell in enumerate(header_cells):
        text = cell.text.strip()
        if text == "Symbol":
            symbol_idx = i
        elif text == "Company Name":
            company_name_idx = i

    if symbol_idx is None or company_name_idx is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not find Symbol or Company Name columns"
        )

    # Process table rows
    rows = table.find_all('tr')[1:]  # Skip header row
    codes_updated = 0

    for row in rows:
        cells = row.find_all('td')
        if len(cells) > max(symbol_idx, company_name_idx):
            code = cells[symbol_idx].text.strip()
            name = cells[company_name_idx].text.strip()

            # Check if code exists
            result = await db.execute(
                sql_text("SELECT id FROM dse_schema.trading_codes WHERE code = :code"),
                {"code": code}
            )
            existing = result.scalar_one_or_none()

            if existing:
                # Update existing code
                await db.execute(
                    sql_text("UPDATE dse_schema.trading_codes SET name = :name WHERE code = :code"),
                    {"name": name, "code": code}
                )
            else:
                # Insert new code
                await db.execute(
                    sql_text("INSERT INTO dse_schema.trading_codes (code, name) VALUES (:code, :name)"),
                    {"code": code, "name": name}
                )

            codes_updated += 1

    await db.commit()

    return {"status": "success", "codes_updated": codes_updated}
