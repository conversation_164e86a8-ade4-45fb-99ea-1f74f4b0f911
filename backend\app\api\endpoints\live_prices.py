import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import requests
from app.api.deps import get_current_user, get_db
from app.models.portfolio_entry import PortfolioEntry
from app.models.trading_code import TradingCode
from app.models.user import User
from app.schemas.live_price import (LivePrice, LivePriceResponse, StockDetails,
                                    StockDetailsResponse)
from bs4 import BeautifulSoup
from fastapi import (APIRouter, BackgroundTasks, Depends, HTTPException, Query,
                     status)
# from lxml import etree  # Not needed anymore
from sqlalchemy import distinct, select
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

# Cache for live prices
# Structure: {trading_code: {"price": float, "last_updated": str}}
live_prices_cache: Dict[str, LivePrice] = {}

# Cache for stock details
# Structure: {trading_code: StockDetails}
stock_details_cache: Dict[str, StockDetails] = {}

# Flag to track if the background task is running
is_background_task_running = False

# Logger
logger = logging.getLogger(__name__)


async def fetch_live_price(trading_code: str) -> Optional[float]:
    """
    Fetch live price for a trading code from dsebd.org
    """
    try:
        url = f"https://www.dsebd.org/displayCompany.php?name={trading_code}"
        logger.info(f"Fetching price for {trading_code} from {url}")

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(url, headers=headers, timeout=15)

        if response.status_code != 200:
            logger.error(f"Failed to fetch data for {trading_code}: Status code {response.status_code}")
            return None

        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Look for "Last Trading Price" in table headers
        price_value = None

        # Find all table rows
        rows = soup.find_all('tr')
        for row in rows:
            # Find header cells that contain "Last Trading Price"
            header_cells = row.find_all('th')
            for header in header_cells:
                if 'Last Trading Price' in header.text:
                    # Found the header, now get the corresponding value cell
                    value_cell = row.find('td')
                    if value_cell:
                        price_value = value_cell.text.strip()
                        logger.info(f"Found Last Trading Price for {trading_code}: {price_value}")
                        break

            if price_value:
                break

        # If we didn't find it with the above method, try another approach
        if not price_value:
            # Try to find tables with both th and td elements
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    th = row.find('th')
                    td = row.find('td')
                    if th and td and 'Last Trading Price' in th.text:
                        price_value = td.text.strip()
                        logger.info(f"Found Last Trading Price in table for {trading_code}: {price_value}")
                        break

                if price_value:
                    break

        # Process the price if found
        if price_value:
            # Clean and convert price to float
            price_text = price_value.replace(',', '')
            try:
                price = float(price_text)
                logger.info(f"Successfully extracted price for {trading_code}: {price}")
                return price
            except ValueError:
                logger.error(f"Could not convert price to float for {trading_code}: {price_text}")
                return None

        # If we got here, we couldn't find the price
        logger.error(f"Could not find 'Last Trading Price' for {trading_code}")

        # Save the HTML for debugging
        with open(f"debug_{trading_code}.html", "w", encoding="utf-8") as f:
            f.write(str(soup))
        logger.info(f"Saved HTML to debug_{trading_code}.html for debugging")

        return None

    except Exception as e:
        logger.error(f"Error fetching live price for {trading_code}: {str(e)}")
        return None


async def fetch_stock_details(trading_code: str) -> Optional[StockDetails]:
    """
    Fetch additional stock details for a trading code from dsebd.org
    """
    try:
        url = f"https://www.dsebd.org/displayCompany.php?name={trading_code}"
        logger.info(f"Fetching stock details for {trading_code} from {url}")

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(url, headers=headers, timeout=15)

        if response.status_code != 200:
            logger.error(f"Failed to fetch data for {trading_code}: Status code {response.status_code}")
            return None

        soup = BeautifulSoup(response.content, 'html.parser')

        opening_price = None
        week_52_low = None
        week_52_high = None
        pe_ratio = None

        rows = soup.find_all('tr')

        for row in rows:
            th = row.find('th')
            if th:
                header_text = th.text.strip()

                if 'Opening Price' in header_text:
                    td = row.find('td') 
                    if td:
                        value_text = td.text.strip()
                        try:
                            opening_price = float(value_text.replace(',', ''))
                        except ValueError:
                            logger.warning(f"Could not parse Opening Price for {trading_code}: {value_text}")

                elif "52 Weeks' Moving Range" in header_text:
                    value_td = th.find_next_sibling('td')
                    if value_td:
                        value_text = value_td.text.strip()
                        try:
                            if ' - ' in value_text:
                                low_value = value_text.split(' - ')[0].strip()
                                high_value = value_text.split(' - ')[1].strip()
                                week_52_low = float(low_value.replace(',', ''))
                                week_52_high = float(high_value.replace(',', ''))
                                logger.info(f"Found 52W Low & High for {trading_code}: {week_52_low} - {week_52_high}")
                            else:
                                logger.warning(f"52W range format unexpected for {trading_code}: {value_text}")
                        except (ValueError, TypeError):
                            logger.warning(f"Could not parse 52W Low for {trading_code}: {value_text}")
                    else:
                        logger.warning(f"Found header '52 Weeks' Moving Range' but no corresponding value cell for {trading_code}")

        try:
            pe_header = soup.find('h2', string=lambda text: text and "Price Earnings (P/E) Ratio Based on latest Audited Financial Statements" in text)

            if pe_header:
                container_div = pe_header.find_next_sibling('div')

                if container_div:
                    pe_table = container_div.find('table')

                    if pe_table:
                        all_rows = pe_table.find_all('tr')
                        if len(all_rows) > 1:
                            second_row = all_rows[1]
                            td_elements = second_row.find_all('td')

                            if td_elements:
                                last_td = td_elements[-1]
                                value_text = last_td.text.strip()

                                try:
                                    pe_ratio = float(value_text.replace(',', ''))
                                    logger.info(f"Found P/E Ratio for {trading_code}: {pe_ratio}")
                                except (ValueError, TypeError):
                                    logger.warning(f"Could not parse P/E Ratio from second row for {trading_code}: '{value_text}'")
                            else:
                                logger.warning(f"P/E table's second row has no 'td' elements for {trading_code}.")
                        else:
                            logger.warning(f"P/E table for {trading_code} has less than two rows.")
                    else:
                        logger.warning(f"Found container div for P/E, but no 'table' was found inside it for {trading_code}.")
                else:
                    logger.warning(f"Found P/E header for {trading_code}, but its next sibling was not a 'div'.")
            else:
                logger.warning(f"Could not find the P/E Ratio header for {trading_code} on the page.")

        except Exception as e:
            logger.error(f"An unexpected error occurred while finding P/E ratio for {trading_code}: {e}")

        stock_details = StockDetails(
            trading_code=trading_code,
            opening_price=opening_price,
            week_52_low=week_52_low,
            week_52_high=week_52_high,
            pe_ratio=pe_ratio,
            last_updated=datetime.now().isoformat()
        )

        logger.info(f"Successfully extracted stock details for {trading_code}: "
                   f"Opening={opening_price}, 52W Low={week_52_low}, 52W High={week_52_high}, P/E={pe_ratio}")

        return stock_details

    except Exception as e:
        logger.error(f"Error fetching stock details for {trading_code}: {str(e)}")
        return None


async def update_live_prices_task(db: AsyncSession):
    """
    Background task to update live prices for all trading codes in users' portfolios
    """
    global is_background_task_running

    if is_background_task_running:
        logger.info("Background task already running, skipping")
        return

    try:
        is_background_task_running = True
        logger.info("Starting background task to update live prices")

        # Get all unique trading codes from portfolio entries
        result = await db.execute(
            select(TradingCode.code)
            .join(PortfolioEntry, PortfolioEntry.trading_code_id == TradingCode.id)
            .distinct()
        )
        trading_codes = [row[0] for row in result.all()]

        if not trading_codes:
            logger.info("No trading codes found in portfolios")
            return

        logger.info(f"Fetching live prices for {len(trading_codes)} trading codes")

        # Fetch prices for each trading code
        for code in trading_codes:
            price = await fetch_live_price(code)
            if price is not None:
                # Update cache
                live_prices_cache[code] = LivePrice(
                    trading_code=code,
                    price=price,
                    last_updated=datetime.now().isoformat()
                )
                logger.info(f"Updated price for {code}: {price}")

            # Sleep briefly to avoid overwhelming the server
            await asyncio.sleep(1)

        logger.info("Finished updating live prices")

    except Exception as e:
        logger.error(f"Error in background task: {str(e)}")
    finally:
        is_background_task_running = False


@router.get("", response_model=LivePriceResponse)
async def get_live_prices(
    codes: str = Query(None, description="Comma-separated list of trading codes"),
    background_tasks: BackgroundTasks = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get live prices for specific trading codes
    """
    # Update cache access time
    from app.core.scheduler import update_cache_access_time
    update_cache_access_time()

    if codes:
        # Parse the comma-separated list of trading codes
        trading_codes = [code.strip().upper() for code in codes.split(",")]
        logger.info(f"Fetching live prices for specific codes: {trading_codes}")

        # Fetch prices for the requested trading codes
        result = {}
        for code in trading_codes:
            # Check if we have a cached price that's less than 5 minutes old
            if code in live_prices_cache:
                last_updated = datetime.fromisoformat(live_prices_cache[code].last_updated)
                now = datetime.now()
                age_seconds = (now - last_updated).total_seconds()

                if age_seconds < 300:  # 5 minutes
                    result[code] = live_prices_cache[code]
                    continue

            # Fetch fresh price
            price = await fetch_live_price(code)
            if price is not None:
                live_price = LivePrice(
                    trading_code=code,
                    price=price,
                    last_updated=datetime.now().isoformat()
                )
                live_prices_cache[code] = live_price
                result[code] = live_price

        return {"prices": result}
    else:
        # Start background task to update all prices if not already running
        if background_tasks and not is_background_task_running:
            background_tasks.add_task(update_live_prices_task, db)

        return {"prices": live_prices_cache}


@router.get("/refresh", response_model=LivePriceResponse)
async def refresh_live_prices(
    codes: str = Query(None, description="Comma-separated list of trading codes to refresh"),
    background_tasks: BackgroundTasks = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Force refresh of live prices
    """
    # Update cache access time
    from app.core.scheduler import update_cache_access_time
    update_cache_access_time()

    if codes:
        # Parse the comma-separated list of trading codes
        trading_codes = [code.strip().upper() for code in codes.split(",")]
        logger.info(f"Refreshing live prices for specific codes: {trading_codes}")

        # Clear cache for the specified trading codes
        for code in trading_codes:
            if code in live_prices_cache:
                del live_prices_cache[code]

        # Fetch fresh prices for the requested trading codes
        result = {}
        for code in trading_codes:
            price = await fetch_live_price(code)
            if price is not None:
                live_price = LivePrice(
                    trading_code=code,
                    price=price,
                    last_updated=datetime.now().isoformat()
                )
                live_prices_cache[code] = live_price
                result[code] = live_price

        logger.info(f"Refresh completed for {len(result)} trading codes by user {current_user.id}")
        return {"prices": result}
    else:
        # Clear all cache and start background task
        live_prices_cache.clear()

        if background_tasks:
            background_tasks.add_task(update_live_prices_task, db)
            logger.info(f"Full refresh requested by user {current_user.id}")

        return {"prices": live_prices_cache}


# @router.get("/stock-details", response_model=StockDetailsResponse)
# async def get_stock_details(
#     codes: str = Query(..., description="Comma-separated list of trading codes"),
#     current_user: User = Depends(get_current_user),
# ) -> Any:
#     """
#     Get stock details (Opening Price, 52W Low, P/E Ratio) for specific trading codes
#     """
#     # Parse the comma-separated list of trading codes
#     trading_codes = [code.strip().upper() for code in codes.split(",")]
#     logger.info(f"Fetching stock details for codes: {trading_codes}")

#     result = {}
#     for code in trading_codes:
#         # Check if we have cached details that are less than 24 hours old
#         if code in stock_details_cache:
#             last_updated = datetime.fromisoformat(stock_details_cache[code].last_updated)
#             now = datetime.now()
#             age_hours = (now - last_updated).total_seconds() / 3600

#             if age_hours < 24:  # 24 hours
#                 result[code] = stock_details_cache[code]
#                 logger.info(f"Using cached stock details for {code}")
#                 continue

#         # Fetch fresh stock details
#         stock_details = await fetch_stock_details(code)
#         if stock_details is not None:
#             stock_details_cache[code] = stock_details
#             result[code] = stock_details
#             logger.info(f"Fetched fresh stock details for {code}")

#         # Sleep briefly to avoid overwhelming the server
#         await asyncio.sleep(1)

#     return {"details": result}

@router.get("/stock-details", response_model=StockDetailsResponse)
async def get_stock_details(
    codes: str = Query(..., description="Comma-separated list of trading codes"),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get stock details (Opening Price, 52W Low, P/E Ratio) for specific trading codes.
    """
    # Parse the comma-separated list of trading codes
    trading_codes = [code.strip().upper() for code in codes.split(",")]
    logger.info(f"Fetching fresh stock details for codes: {trading_codes}")

    result = {}
    for code in trading_codes:
        # Fetch fresh stock details for each code as requested
        stock_details = await fetch_stock_details(code)
        if stock_details is not None:
            result[code] = stock_details
            logger.info(f"Fetched fresh stock details for {code}")

        # Sleep briefly to avoid overwhelming the underlying data source
        await asyncio.sleep(1)

    return {"details": result}