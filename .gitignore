# Python virtual environment
venv/
env/
.env

# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js
.next/
out/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Database
*.sqlite3
*.db

# Logs
logs/
*.log

# OS specific files
.DS_Store
Thumbs.db
