"use client";

import DashboardLayout from "@/components/dashboard-layout";
import { Button } from "@/components/ui/button";
import { useLivePrices } from "@/hooks/use-live-prices";
import { useNotificationPermission } from "@/hooks/use-notification-permission";
import { useStockDetails } from "@/hooks/use-stock-details";
import { useToast } from "@/hooks/use-toast";
//import { showNotification } from "@/lib/notificationService";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import PortfolioTable, {
  PortfolioHolding,
} from "../../components/portfolio/PortfolioTable";

export default function DashboardPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [marketOpen, setMarketOpen] = useState<boolean>(false);
  const [basePortfolio, setBasePortfolio] = useState<PortfolioHolding[]>([]);
  const [investmentAmount, setInvestmentAmount] = useState(0);

  const initialDetailsFetched = useRef(false);

  const { permission, requestPermission } = useNotificationPermission();

  const {
    livePrices,
    isLoading: livePricesLoading,
    setDashboardActive,
    refreshPrices,
    isMarketOpen,
  } = useLivePrices();

  const {
    stockDetails,
    isLoading: stockDetailsLoading,
    fetchStockDetails,
    refreshStockDetails,
  } = useStockDetails();

  const tradingCodesJson = useMemo(
    () => JSON.stringify(basePortfolio.map((item) => item.tradingCode).sort()),
    [basePortfolio]
  );

  interface PortfolioEntry {
    trading_code: {
      code: string;
    };
    transaction_date: string;
    transaction_type: "buy" | "sell";
    unit_count: number;
    transaction_price: number;
  }

  useEffect(() => {
    const checkMarketStatusAndUpdateState = async () => {
      try {
        const isOpen = await isMarketOpen();
        setMarketOpen(isOpen);
      } catch (error) {
        console.error("Error checking market status:", error);
        setMarketOpen(false);
      }
    };

    checkMarketStatusAndUpdateState();
    const statusInterval = setInterval(checkMarketStatusAndUpdateState, 300000); // 5 minutes

    return () => {
      clearInterval(statusInterval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const tradingCodes: string[] = JSON.parse(tradingCodesJson);
    if (tradingCodes.length > 0) {
      setDashboardActive(true, tradingCodes);
    }
    return () => {
      setDashboardActive(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tradingCodesJson]);

  useEffect(() => {
    if (tradingCodesJson === "[]" || initialDetailsFetched.current) {
      return;
    }

    const tradingCodes: string[] = JSON.parse(tradingCodesJson);
    if (tradingCodes.length > 0) {
      console.log("Performing initial fetch for stock details");
      // showNotification("Fetching Stock Details", {
      //   body: "Performing initial fetch for stock details",
      //   tag: "stock-details",
      // });
      fetchStockDetails(tradingCodes);
      initialDetailsFetched.current = true;
    }
  }, [tradingCodesJson, fetchStockDetails]);

  useEffect(() => {
    const intervalId = setInterval(async () => {
      try {
        const now = new Date();
        const hour = now.getHours();
        const marketIsOpenToday = await isMarketOpen();

        if (marketIsOpenToday && hour >= 10 && hour < 11) {
          const tradingCodes: string[] = JSON.parse(tradingCodesJson);
          if (tradingCodes.length > 0) {
            console.log("Performing scheduled refresh for stock details");
            // Here you can use refreshStockDetails if you want a distinct action
            // or just fetchStockDetails again.
            refreshStockDetails(tradingCodes);
          }
        }
      } catch (error) {
        console.error("Error during scheduled fetch interval:", error);
      }
    }, 900000); // 15 minutes

    // The cleanup function will clear the interval when the component unmounts
    // or when the dependencies change, preventing memory leaks.
    return () => clearInterval(intervalId);
  }, [tradingCodesJson, isMarketOpen, refreshStockDetails]);

  const processPortfolioEntries = useCallback(
    (entries: PortfolioEntry[]): PortfolioHolding[] => {
      if (!entries || entries.length === 0) return [];

      const validEntries = entries.filter((entry) => entry.trading_code?.code);

      if (validEntries.length === 0) return [];

      const groupedEntries = validEntries.reduce<
        Record<string, PortfolioEntry[]>
      >((acc, entry) => {
        const { code } = entry.trading_code;
        if (!acc[code]) {
          acc[code] = [];
        }
        acc[code].push(entry);
        return acc;
      }, {});

      return Object.entries(groupedEntries)
        .map(([tradingCode, stockEntries]) => {
          const sortedEntries = [...stockEntries].sort(
            (a, b) =>
              new Date(a.transaction_date).getTime() -
              new Date(b.transaction_date).getTime()
          );

          let totalShares = 0;
          let totalCost = 0;
          let lastTransactionDate = sortedEntries[0]?.transaction_date;

          sortedEntries.forEach((entry) => {
            const {
              transaction_type,
              unit_count,
              transaction_price,
              transaction_date,
            } = entry;

            if (
              lastTransactionDate &&
              new Date(transaction_date) > new Date(lastTransactionDate)
            ) {
              lastTransactionDate = transaction_date;
            }

            if (transaction_type === "buy") {
              totalCost += unit_count * transaction_price;
              totalShares += unit_count;
            } else if (transaction_type === "sell") {
              if (totalShares > 0) {
                const avgCost = totalCost / totalShares;
                totalCost -= unit_count * avgCost;
                totalShares -= unit_count;
              }
            }
          });

          if (totalShares <= 0) return null;

          const avgPrice = totalCost / totalShares;

          return {
            tradingCode,
            shareCount: totalShares,
            avgPrice,
            totalInvestment: totalCost,
            lastBuyDate: lastTransactionDate || new Date().toISOString(),
            currentPrice: avgPrice,
          };
        })
        .filter((entry): entry is PortfolioHolding => entry !== null);
    },
    []
  );

  const fetchPortfolioData = useCallback(async () => {
    if (!session?.user?.accessToken) return;
    setIsLoading(true);
    try {
      const [entriesRes, settingsRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`, {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`, {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }),
      ]);
      if (entriesRes.ok) {
        const entries = await entriesRes.json();
        const processed = processPortfolioEntries(entries);
        setBasePortfolio(processed);
      } else throw new Error("Failed to fetch portfolio entries");
      if (settingsRes.ok) {
        const settings = await settingsRes.json();
        setInvestmentAmount(settings.investment_amount || 0);
      }
    } catch (error) {
      console.error("Error fetching portfolio data:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while fetching your portfolio.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [session, processPortfolioEntries, toast]);

  useEffect(() => {
    fetchPortfolioData();
  }, [fetchPortfolioData]);

  const portfolioForDisplay = useMemo(() => {
    return basePortfolio
      .map((holding) => {
        const livePriceData = livePrices[holding.tradingCode];
        const stockDetailData = stockDetails[holding.tradingCode];

        const currentPrice = livePriceData?.price;
        const openingPrice = stockDetailData?.opening_price;

        let changeDirection: "up" | "down" | "none" | undefined;
        let changePercent: number | undefined;

        if (currentPrice != null && openingPrice != null && openingPrice > 0) {
          const change = currentPrice - openingPrice;
          changePercent = (change / openingPrice) * 100;
          if (change > 0) {
            changeDirection = "up";
          } else if (change < 0) {
            changeDirection = "down";
          } else {
            changeDirection = "none";
          }
        }

        return {
          ...holding,
          currentPrice: currentPrice || holding.currentPrice,
          isLivePrice: !!livePriceData,
          changeDirection,
          changePercent,
          openingPrice,
          week52Low: stockDetailData?.week_52_low,
          week52High: stockDetailData?.week_52_high,
          peRatio: stockDetailData?.pe_ratio,
        };
      })
      .sort((a, b) => a.tradingCode.localeCompare(b.tradingCode));
  }, [basePortfolio, livePrices, stockDetails]);

  const handleRefreshPrices = useCallback(() => {
    const tradingCodes: string[] = JSON.parse(tradingCodesJson);
    if (tradingCodes.length > 0) {
      refreshPrices(tradingCodes);
    }
  }, [refreshPrices, tradingCodesJson]);

  const handleRefreshStockDetails = useCallback(() => {
    const tradingCodes: string[] = JSON.parse(tradingCodesJson);
    console.log("will refresh stock details", tradingCodes);
    if (tradingCodes.length > 0) {
      refreshStockDetails(tradingCodes);
    }
  }, [refreshStockDetails, tradingCodesJson]);

  const lastUpdatedTime = useMemo(() => {
    const timestamps = Object.values(livePrices)
      .map((price) => new Date(price.last_updated).getTime())
      .filter((ts) => !isNaN(ts));
    if (timestamps.length === 0) return null;
    return new Date(Math.max(...timestamps)).toLocaleTimeString();
  }, [livePrices]);

  return (
    <DashboardLayout>
      {permission === "default" && (
        <div className="mb-4 flex items-center justify-center rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
          <p className="text-sm">
            Enable browser notifications to get real-time market alerts.
          </p>
          <Button onClick={requestPermission} size="sm" className="ml-4">
            Enable Notifications
          </Button>
        </div>
      )}

      {permission === "denied" && (
        <div className="mb-4 flex items-center justify-center rounded-lg border bg-yellow-100 p-4 text-yellow-800 shadow-sm">
          <p className="text-sm">
            You have blocked notifications. To receive alerts, please enable
            them in your browser settings.
          </p>
        </div>
      )}
      <PortfolioTable
        holdings={portfolioForDisplay}
        lastUpdatedTime={lastUpdatedTime}
        isMarketOpen={marketOpen}
        isLoading={isLoading}
        onEntryAdded={fetchPortfolioData}
        investmentAmount={investmentAmount}
        onRefreshPrices={handleRefreshPrices}
        isRefreshingPrices={livePricesLoading}
        onRefreshStockDetails={handleRefreshStockDetails}
        isRefreshingStockDetails={stockDetailsLoading}
      />
    </DashboardLayout>
  );
}
