import asyncio
import json
import logging
from typing import Dict, List

from app.core.scheduler import (add_active_user, live_prices_cache,
                                remove_active_user)
from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect
from fastapi.security import OAuth2PasswordBearer

router = APIRouter()

# Logger
logger = logging.getLogger(__name__)

# Connected WebSocket clients
connected_clients: Dict[str, WebSocket] = {}

# OAuth2 scheme for WebSocket authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


@router.websocket("/websocket/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """
    WebSocket endpoint for live price updates
    """
    logger.info(f"WebSocket connection request from user {user_id}")
    logger.info(f"WebSocket request URL: {websocket.url}")
    logger.info(f"WebSocket request headers: {websocket.headers}")

    try:
        await websocket.accept()
        logger.info(f"WebSocket accepted for user {user_id}")
        connected_clients[user_id] = websocket

        # Add user to active dashboard users
        add_active_user(user_id)

        logger.info(f"WebSocket connection established for user {user_id}")
    except Exception as e:
        logger.error(f"Error accepting WebSocket connection for user {user_id}: {str(e)}")
        return

    try:
        # Send initial data
        if live_prices_cache:
            try:
                data = {code: price.model_dump() for code, price in live_prices_cache.items()}
                await websocket.send_json({
                    "type": "live_prices",
                    "data": data
                })
                logger.info(f"Sent initial live prices to user {user_id}: {len(data)} items")
            except Exception as e:
                logger.error(f"Error sending initial live prices to user {user_id}: {str(e)}")

        # Keep connection alive and handle messages
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            message = json.loads(data)

            # Handle different message types
            if message.get("type") == "ping":
                await websocket.send_json({"type": "pong"})
            elif message.get("type") == "dashboard_active":
                # User is on dashboard page
                add_active_user(user_id)
            elif message.get("type") == "dashboard_inactive":
                # User left dashboard page
                remove_active_user(user_id)

    except WebSocketDisconnect:
        logger.info(f"WebSocket connection closed for user {user_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {str(e)}")
    finally:
        # Clean up
        if user_id in connected_clients:
            del connected_clients[user_id]
        remove_active_user(user_id)


@router.websocket("/ws/{user_id}")
async def websocket_endpoint_alt(websocket: WebSocket, user_id: str):
    """
    Alternative WebSocket endpoint for live price updates
    """
    logger.info(f"Alternative WebSocket connection request from user {user_id}")
    logger.info(f"WebSocket request URL: {websocket.url}")
    logger.info(f"WebSocket request headers: {websocket.headers}")

    # Redirect to the main endpoint
    await websocket_endpoint(websocket, user_id)


async def broadcast_live_prices():
    """
    Broadcast live prices to all connected clients
    """
    if not connected_clients or not live_prices_cache:
        return

    try:
        data = {code: price.model_dump() for code, price in live_prices_cache.items()}
        message = {
            "type": "live_prices",
            "data": data
        }
        logger.info(f"Broadcasting live prices to {len(connected_clients)} clients: {len(data)} items")
    except Exception as e:
        logger.error(f"Error preparing live prices broadcast: {str(e)}")
        return

    # Send to all connected clients
    for user_id, websocket in list(connected_clients.items()):
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.error(f"Error sending to client {user_id}: {str(e)}")
            # Remove client if we can't send to it
            if user_id in connected_clients:
                del connected_clients[user_id]
