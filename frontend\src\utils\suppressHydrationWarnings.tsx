"use client";

import { ReactNode, useEffect } from "react";

/**
 * Utility to suppress React hydration warnings
 * This is especially useful for browser extensions that modify the DOM
 */
export function useSuppressHydrationWarnings() {
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Store the original console.error
      const originalConsoleError = console.error;

      // Override console.error to filter out hydration warnings
      console.error = (...args: unknown[]) => {
        // Check if this is a hydration warning
        const isHydrationWarning = args.some(
          (arg) =>
            typeof arg === "string" &&
            (arg.includes("Hydration failed because") ||
              arg.includes("Warning: Text content did not match") ||
              arg.includes("Warning: Expected server HTML to contain") ||
              arg.includes("Warning: Prop") ||
              arg.includes("did not match") ||
              arg.includes("A tree hydrated but some attributes") ||
              arg.includes("rp-extension"))
        );

        // Only log to console if it's not a hydration warning
        if (!isHydrationWarning) {
          originalConsoleError(...args);
        }
      };

      // Cleanup function to restore original console.error
      return () => {
        console.error = originalConsoleError;
      };
    }

    return () => {};
  }, []);
}

interface SuppressHydrationWarningsProps {
  children: ReactNode;
}

/**
 * Component wrapper to suppress hydration warnings
 */
export function SuppressHydrationWarnings({
  children,
}: SuppressHydrationWarningsProps) {
  useSuppressHydrationWarnings();

  // Just return children directly to avoid adding extra DOM elements
  // The parent elements already have suppressHydrationWarning
  return <>{children}</>;
}
