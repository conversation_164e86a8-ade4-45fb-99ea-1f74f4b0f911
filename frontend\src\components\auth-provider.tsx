"use client";

import { AuthContextProvider } from "@/context/auth-context";
import { SessionProvider } from "next-auth/react";
import ErrorBoundary from "./error-boundary";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div>Something went wrong. Please try refreshing the page.</div>
      }
    >
      <SessionProvider refetchOnWindowFocus={false}>
        <AuthContextProvider>{children}</AuthContextProvider>
      </SessionProvider>
    </ErrorBoundary>
  );
}
