# Production Build Guide

This guide explains how to build and run the DSE Analyzen application in production mode.

## NextAuth.js v5 Production Configuration

The application has been configured to work with NextAuth.js v5 in production builds. The following changes have been made:

### Environment Variables

The application now supports both legacy and new NextAuth.js environment variables:

**Required Environment Variables:**

- `AUTH_URL` - Your application URL (e.g., `http://localhost:3000` for local, `https://yourdomain.com` for production)
- `AUTH_SECRET` - A secure random string (generated using `openssl rand -hex 32`)
- `NEXTAUTH_URL` - Legacy support (same as AUTH_URL)
- `NEXTAUTH_SECRET` - Legacy support (same as AUTH_SECRET)
- `NEXT_PUBLIC_API_URL` - Backend API URL

### Configuration Changes

1. **Added `trustHost: true`** to NextAuth configuration (required for v5 production builds)
2. **Updated environment variable handling** to support both new and legacy variable names
3. **Added standalone output** configuration for optimized production builds

## Building for Production

### Option 1: Local Production Build

1. **Build the application:**

   ```bash
   cd frontend
   npm run build
   ```

2. **Start the production server:**
   ```bash
   npm run start
   ```

### Option 2: Using Docker (Recommended)

1. **Build and run with production Docker Compose:**

   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. **Or build the production Docker image manually:**
   ```bash
   cd frontend
   docker build -f Dockerfile.prod -t dse-analyzen-frontend .
   docker run -p 3000:3000 \
     -e AUTH_URL=http://localhost:3000 \
     -e AUTH_SECRET=your-secret-here \
     -e NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1 \
     dse-analyzen-frontend
   ```

## Environment Configuration

### Development (.env.local)

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=0788c201cb981bea46d2d971ba806627d1e9981b9094984c785795da7f21d492
AUTH_URL=http://localhost:3000
AUTH_SECRET=0788c201cb981bea46d2d971ba806627d1e9981b9094984c785795da7f21d492
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
```

### Production (.env.production)

Update these values for your production deployment:

```env
AUTH_URL=https://yourdomain.com
AUTH_SECRET=your-production-secret-here
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-production-secret-here
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
```

## Security Notes

1. **Generate a new AUTH_SECRET for production:**

   ```bash
   openssl rand -hex 32
   ```

2. **Update AUTH_URL and NEXTAUTH_URL** to your production domain

3. **Use HTTPS in production** for security

## Troubleshooting

### "UntrustedHost" Error

This error occurs when NextAuth.js doesn't trust the host. The fix:

- Ensure `trustHost: true` is set in the NextAuth configuration
- Verify `AUTH_URL` matches your deployment URL
- Check that `AUTH_SECRET` is properly set

### JWT Session Errors ("no matching decryption secret")

This error occurs when NextAuth.js tries to decrypt tokens with a different secret:

**Quick Fix:**

1. Clear existing sessions:

   ```bash
   cd frontend
   npm run clear-sessions
   ```

2. Rebuild the application:

   ```bash
   npm run build
   ```

3. Clear browser data:

   - Open browser developer tools (F12)
   - Go to Application/Storage tab
   - Clear all cookies for localhost:3000
   - Clear localStorage and sessionStorage

4. Start fresh:
   ```bash
   npm run start
   ```

**Alternative Fix:**
Generate a new secret and update environment variables:

```bash
# Generate new secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Update .env.local with the new secret
AUTH_SECRET=your-new-secret-here
NEXTAUTH_SECRET=your-new-secret-here
```

### Build Errors

- Ensure all environment variables are properly set
- Check that the backend API is accessible from the frontend
- Verify Node.js version compatibility (Node 18+ recommended)
- If build cache issues occur, run `npm run reset` to clear cache and rebuild

## Files Modified

- `frontend/src/auth.ts` - Added `trustHost: true` and updated environment variable handling
- `frontend/.env.local` - Added AUTH_URL and AUTH_SECRET variables
- `frontend/.env.production` - Production environment template
- `frontend/next.config.js` - Added standalone output configuration
- `frontend/package.json` - Added production build scripts
- `frontend/Dockerfile.prod` - Production Docker configuration
- `docker-compose.prod.yml` - Production Docker Compose configuration

## Next Steps

1. Test the production build locally
2. Update environment variables for your production environment
3. Deploy using your preferred method (Docker, Vercel, etc.)
4. Ensure HTTPS is configured for production deployments
