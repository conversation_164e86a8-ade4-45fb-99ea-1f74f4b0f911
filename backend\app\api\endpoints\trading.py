from typing import Any, List

from app.api.deps import get_current_user, get_db
from app.models.trading_code import TradingCode
from app.models.user import User
from app.schemas.trading import TradingCodeResponse
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

router = APIRouter()

@router.get("", response_model=List[TradingCodeResponse])
async def get_trading_codes(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all trading codes
    """
    result = await db.execute(select(TradingCode).order_by(TradingCode.code))
    trading_codes = result.scalars().all()

    return trading_codes
