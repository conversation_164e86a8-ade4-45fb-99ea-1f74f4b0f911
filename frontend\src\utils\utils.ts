interface MarketStatusResponse {
  MarketStatus: string;
  // Add other properties from the API response if needed
}

interface CachedMarketStatus {
  status: boolean;
  date: string; // To store the date for which the status was fetched (YYYY-MM-DD)
}

const MARKET_STATUS_KEY = "marketStatus";
const MARKET_OPEN_TIME_HOUR = 10;
const MARKET_OPEN_TIME_MINUTE = 0;
const MARKET_CLOSE_TIME_HOUR = 14;
const MARKET_CLOSE_TIME_MINUTE = 30;
const FRIDAY_DAY_INDEX = 5; // JavaScript's getDay() returns 0 for Sunday, 5 for Friday

/**
 * Checks if the stock market is currently open.
 *
 * @returns {Promise<boolean>} True if the market is open, false otherwise.
 */
export const isMarketOpen = async (): Promise<boolean> => {
  const now = new Date();
  const currentDay = now.getDay();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const todayDateString = now.toISOString().split("T")[0]; // YYYY-MM-DD

  // 1. Check if it's Friday
  if (currentDay === FRIDAY_DAY_INDEX) {
    // console.log('Market closed: It is Friday.');
    return false;
  }

  // 2. Check if current time is outside market hours (10:00 AM to 2:30 PM)
  const isBeforeMarketOpen =
    currentHour < MARKET_OPEN_TIME_HOUR ||
    (currentHour === MARKET_OPEN_TIME_HOUR &&
      currentMinute < MARKET_OPEN_TIME_MINUTE);

  const isAfterMarketClose =
    currentHour > MARKET_CLOSE_TIME_HOUR ||
    (currentHour === MARKET_CLOSE_TIME_HOUR &&
      currentMinute >= MARKET_CLOSE_TIME_MINUTE);

  if (isBeforeMarketOpen || isAfterMarketClose) {
    // console.log('Market closed: Outside trading hours.');
    // Clear localStorage if the trading window for the day has passed,
    // so it fetches fresh data on the next trading day.
    if (isAfterMarketClose) {
      const cachedDataString = localStorage.getItem(MARKET_STATUS_KEY);
      if (cachedDataString) {
        const cachedData: CachedMarketStatus = JSON.parse(cachedDataString);
        if (cachedData.date === todayDateString) {
          localStorage.removeItem(MARKET_STATUS_KEY);
          // console.log('Cleared market status from localStorage as trading window passed.');
        }
      }
    }
    return false;
  }

  // 3. If within 10:00 AM - 2:30 PM and not Friday, check localStorage
  const cachedDataString = localStorage.getItem(MARKET_STATUS_KEY);
  if (cachedDataString) {
    try {
      const cachedData: CachedMarketStatus = JSON.parse(cachedDataString);
      if (cachedData.date === todayDateString) {
        return cachedData.status;
      } else {
        localStorage.removeItem(MARKET_STATUS_KEY);
        // console.log('Removed stale market status from localStorage.');
      }
    } catch (error) {
      console.error("Error parsing cached market status:", error);
      localStorage.removeItem(MARKET_STATUS_KEY);
    }
  }

  // 4. If no valid cached data, fetch from the backend API
  // console.log('Fetching market status from backend API...');
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/market/status`
    );
    if (!response.ok) {
      console.error(
        "Failed to fetch market status, API responded with:",
        response.status
      );
      return false;
    }
    const data: MarketStatusResponse = await response.json();
    const marketIsOpen = data.MarketStatus !== "Closed";

    const newCachedData: CachedMarketStatus = {
      status: marketIsOpen,
      date: todayDateString,
    };
    localStorage.setItem(MARKET_STATUS_KEY, JSON.stringify(newCachedData));
    return marketIsOpen;
  } catch (error) {
    console.error("Error fetching or processing market status:", error);
    // In case of a fetch error, it's safer to assume the market might be closed
    return false;
  }
};
