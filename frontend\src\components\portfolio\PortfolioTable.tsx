"use client";

import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { ArrowUpDown, Minus, Plus, RefreshCw, Search } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Button } from "../../components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";

const RefreshButton = React.memo(
  ({
    onClick,
    isLoading,
    tooltipText,
  }: {
    onClick: () => void;
    isLoading: boolean;
    tooltipText: string;
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 ml-1"
            onClick={(e) => {
              e.stopPropagation(); // Prevent column sort from triggering
              onClick();
            }}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`}
            />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
);
RefreshButton.displayName = "RefreshButton";

export interface PortfolioHolding {
  tradingCode: string;
  currentPrice: number;
  shareCount: number;
  avgPrice: number;
  totalInvestment: number;
  lastBuyDate: string;
  isLivePrice?: boolean;
  previousPrice?: number;
  changeDirection?: "up" | "down" | "none";
  changePercent?: number;
  gainLoss?: number;
  openingPrice?: number;
  week52Low?: number;
  week52High?: number;
  peRatio?: number;
}

interface TradingCode {
  id: number;
  code: string;
  name: string;
}

interface PortfolioTableProps {
  holdings: PortfolioHolding[];
  lastUpdatedTime?: string | null;
  isMarketOpen: boolean;
  isLoading?: boolean;
  onEntryAdded?: () => void;
  investmentAmount?: number;
  onRefreshPrices: () => void;
  isRefreshingPrices: boolean;
  onRefreshStockDetails: () => void;
  isRefreshingStockDetails: boolean;
}

const MarketStatusIndicator = React.memo(({ isOpen }: { isOpen: boolean }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <span
          className={`ml-2 inline-block h-3 w-3 rounded-full ${
            isOpen ? "bg-green-500" : "bg-red-500"
          } cursor-help`}
          aria-label={`Market is ${isOpen ? "open" : "closed"}`}
        />
      </TooltipTrigger>
      <TooltipContent>
        <p>Market is {isOpen ? "open" : "closed"}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
));
MarketStatusIndicator.displayName = "MarketStatusIndicator";

type SortField = keyof PortfolioHolding;
type SortDirection = "asc" | "desc";

const formatCurrency = (amount: number) => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return "BDT 0.00";
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatGainLoss = (amount: number) => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return "BDT 0.00";
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 2,
    signDisplay: "never",
  }).format(Math.abs(amount));
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return "N/A";
  }
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export default function PortfolioTable({
  holdings,
  lastUpdatedTime,
  isMarketOpen,
  isLoading: tableIsLoading = false,
  onEntryAdded,
  investmentAmount = 0,
  onRefreshPrices,
  isRefreshingPrices,
  onRefreshStockDetails,
  isRefreshingStockDetails,
}: PortfolioTableProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const router = useRouter();
  const [realizedGain, setRealizedGain] = useState<number>(0);

  const [sortField, setSortField] = useState<SortField>("tradingCode");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [searchQuery, setSearchQuery] = useState("");
  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false);
  const [isSellModalOpen, setIsSellModalOpen] = useState(false);
  const [isAddInvestmentModalOpen, setIsAddInvestmentModalOpen] =
    useState(false);
  const [isAddRealizedGainModalOpen, setIsAddRealizedGainModalOpen] =
    useState(false);
  const [tradingCodes, setTradingCodes] = useState<TradingCode[]>([]);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [commission, setCommission] = useState<number | null>(null);
  const [addInvestmentAmount, setAddInvestmentAmount] = useState("");
  const [addRealizedGainAmount, setAddRealizedGainAmount] = useState("");

  const [buyFormData, setBuyFormData] = useState({
    tradingCodeId: "",
    buyPrice: "",
    unitCount: "",
  });

  const [sellFormData, setSellFormData] = useState({
    tradingCodeId: "",
    sellPrice: "",
    unitCount: "",
  });

  useEffect(() => {
    if (!session?.user?.accessToken) return;
    const fetchTradingCodes = async () => {
      try {
        const cachedData = localStorage.getItem("tradingCodes");
        const cachedTimestamp = localStorage.getItem("tradingCodesTimestamp");
        if (cachedData && cachedTimestamp) {
          const timestamp = parseInt(cachedTimestamp, 10);
          const now = Date.now();
          const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;
          if (now - timestamp < sevenDaysInMs) {
            setTradingCodes(JSON.parse(cachedData));
            return;
          }
        }
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/trading-codes`,
          {
            headers: { Authorization: `Bearer ${session.user.accessToken}` },
          }
        );
        if (response.ok) {
          const data = await response.json();
          setTradingCodes(data);
          localStorage.setItem("tradingCodes", JSON.stringify(data));
          localStorage.setItem("tradingCodesTimestamp", Date.now().toString());
        } else {
          console.error(
            "Failed to fetch trading codes:",
            await response.text()
          );
          if (cachedData) setTradingCodes(JSON.parse(cachedData));
        }
      } catch (error) {
        console.error("Error fetching trading codes:", error);
        const cachedData = localStorage.getItem("tradingCodes");
        if (cachedData) setTradingCodes(JSON.parse(cachedData));
      }
    };
    const fetchCommission = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`,
          {
            headers: { Authorization: `Bearer ${session.user.accessToken}` },
          }
        );
        if (response.ok) {
          const data = await response.json();
          setCommission(data.commission);
          setRealizedGain(data.realized_gain || 0);
        }
      } catch (error) {
        console.error("Error fetching portfolio data:", error);
      }
    };
    fetchTradingCodes();
    fetchCommission();
  }, [session, toast]);

  const handleBuyInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setBuyFormData((prev) => ({ ...prev, [name]: value }));
    },
    []
  );
  const handleSellInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setSellFormData((prev) => ({ ...prev, [name]: value }));
    },
    []
  );
  const handleTradingCodeSelect = useCallback((value: string) => {
    setBuyFormData((prev) => ({ ...prev, tradingCodeId: value }));
  }, []);
  const handleSellTradingCodeSelect = useCallback((value: string) => {
    setSellFormData((prev) => ({ ...prev, tradingCodeId: value }));
  }, []);
  const handleSort = useCallback((field: SortField) => {
    setSortField((currentField) => {
      if (field === currentField) {
        setSortDirection((currentDirection) =>
          currentDirection === "asc" ? "desc" : "asc"
        );
        return currentField;
      } else {
        setSortDirection("asc");
        return field;
      }
    });
  }, []);

  const handleBuySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      !buyFormData.tradingCodeId ||
      !buyFormData.buyPrice ||
      !buyFormData.unitCount
    ) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all fields.",
      });
      return;
    }
    setIsFormSubmitting(true);
    try {
      const buyPrice = parseFloat(buyFormData.buyPrice);
      const unitCount = parseInt(buyFormData.unitCount, 10);
      const finalPrice = commission
        ? buyPrice * (1 + commission / 100)
        : buyPrice;
      const roundedPrice = Math.round(finalPrice * 1000) / 1000;
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            trading_code_id: parseInt(buyFormData.tradingCodeId, 10),
            transaction_price: roundedPrice,
            unit_count: unitCount,
            transaction_date: new Date().toISOString(),
            transaction_type: "buy",
          }),
        }
      );
      if (response.ok) {
        toast({
          variant: "success",
          title: "Success",
          description: "Portfolio entry added successfully.",
        });
        setIsBuyModalOpen(false);
        setBuyFormData({ tradingCodeId: "", buyPrice: "", unitCount: "" });
        onEntryAdded?.();
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to add portfolio entry.",
        });
      }
    } catch (err) {
      console.error("Error adding portfolio entry:", err);
    } finally {
      setIsFormSubmitting(false);
    }
  };
  const handleSellSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      !sellFormData.tradingCodeId ||
      !sellFormData.sellPrice ||
      !sellFormData.unitCount
    ) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all fields.",
      });
      return;
    }
    setIsFormSubmitting(true);
    try {
      const sellPrice = parseFloat(sellFormData.sellPrice);
      const unitCount = parseInt(sellFormData.unitCount, 10);
      const finalPrice = commission
        ? sellPrice * (1 - commission / 100)
        : sellPrice;
      const roundedPrice = Math.round(finalPrice * 1000) / 1000;
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            trading_code_id: parseInt(sellFormData.tradingCodeId, 10),
            transaction_price: roundedPrice,
            unit_count: unitCount,
            transaction_date: new Date().toISOString(),
            transaction_type: "sell",
          }),
        }
      );
      if (response.ok) {
        toast({
          variant: "success",
          title: "Success",
          description: "Sell transaction recorded successfully.",
        });
        setIsSellModalOpen(false);
        setSellFormData({ tradingCodeId: "", sellPrice: "", unitCount: "" });
        onEntryAdded?.();
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to record sell transaction.",
        });
      }
    } catch (err) {
      console.error("Error recording sell transaction:", err);
    } finally {
      setIsFormSubmitting(false);
    }
  };

  const handleAddInvestmentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!addInvestmentAmount || parseFloat(addInvestmentAmount) <= 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please enter a valid investment amount.",
      });
      return;
    }
    setIsFormSubmitting(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/add-investment`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            amount: parseFloat(addInvestmentAmount),
          }),
        }
      );
      if (response.ok) {
        toast({
          variant: "success",
          title: "Success",
          description: "Investment amount added successfully.",
        });
        setIsAddInvestmentModalOpen(false);
        setAddInvestmentAmount("");
        onEntryAdded?.(); // This will refresh the portfolio data
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to add investment amount.",
        });
      }
    } catch (err) {
      console.error("Error adding investment amount:", err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while adding investment amount.",
      });
    } finally {
      setIsFormSubmitting(false);
    }
  };

  const handleAddRealizedGainSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!addRealizedGainAmount || parseFloat(addRealizedGainAmount) <= 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please enter a valid realized gain amount.",
      });
      return;
    }
    setIsFormSubmitting(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/add-realized-gain`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            amount: parseFloat(addRealizedGainAmount),
          }),
        }
      );
      if (response.ok) {
        const data = await response.json();
        setRealizedGain(data.realized_gain || 0);
        toast({
          variant: "success",
          title: "Success",
          description: "Realized gain added successfully.",
        });
        setIsAddRealizedGainModalOpen(false);
        setAddRealizedGainAmount("");
        onEntryAdded?.(); // This will refresh the portfolio data
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to add realized gain.",
        });
      }
    } catch (err) {
      console.error("Error adding realized gain:", err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while adding realized gain.",
      });
    } finally {
      setIsFormSubmitting(false);
    }
  };

  const filteredAndSortedHoldings = useMemo(() => {
    const filtered = holdings.filter((holding) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        holding.tradingCode.toLowerCase().includes(query) ||
        holding.currentPrice.toString().includes(query) ||
        holding.shareCount.toString().includes(query) ||
        holding.totalInvestment.toString().includes(query)
      );
    });
    return [...filtered].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      const aNum = Number(aValue);
      const bNum = Number(bValue);
      return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
    });
  }, [holdings, searchQuery, sortField, sortDirection]);

  const totalInvestment = useMemo(
    () => holdings.reduce((sum, holding) => sum + holding.totalInvestment, 0),
    [holdings]
  );
  const remainingInvestment = useMemo(
    () => Math.max(0, investmentAmount + realizedGain - totalInvestment),
    [investmentAmount, realizedGain, totalInvestment]
  );
  const ownedTradingCodes = useMemo(() => {
    const ownedCodes = new Set(holdings.map((h) => h.tradingCode));
    return tradingCodes.filter((code) => ownedCodes.has(code.code));
  }, [holdings, tradingCodes]);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-xl font-bold flex items-center gap-2">
            Your Portfolio
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/portfolio-details")}
              className="h-6 w-6 p-0 hover:bg-blue-100"
              title="View Portfolio Details"
            >
              <svg
                className="h-4 w-4 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </Button>
          </CardTitle>
          <CardDescription className="flex items-center gap-2">
            Remaining Investment: {formatCurrency(remainingInvestment)}
            <Button
              variant="ghost"
              title="Add Investment"
              size="sm"
              onClick={() => setIsAddInvestmentModalOpen(true)}
              className="h-6 w-6 p-0 hover:bg-green-200"
            >
              <Plus className="h-4 w-4 text-green-600" />
            </Button>
          </CardDescription>
          <CardDescription className="flex items-center gap-2">
            <span>Realized Gain: </span>
            <span className="text-green-600">
              {formatCurrency(realizedGain)}
            </span>
            <Button
              variant="ghost"
              title="Add Realized Gain"
              size="sm"
              onClick={() => setIsAddRealizedGainModalOpen(true)}
              className="h-6 w-6 p-0 hover:bg-green-200"
            >
              <Plus className="h-4 w-4 text-green-600" />
            </Button>
          </CardDescription>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <div className="flex space-x-2">
            <Button
              onClick={() => setIsBuyModalOpen(true)}
              disabled={commission === null || commission === 0}
              size="sm"
              className="flex items-center"
            >
              <Plus className="h-4 w-4" />
              <span className="pb-1">Buy</span>
            </Button>
            <Button
              onClick={() => setIsSellModalOpen(true)}
              disabled={
                commission === null || commission === 0 || holdings.length === 0
              }
              size="sm"
              variant="outline"
              className="flex items-center"
            >
              <Minus className="h-4 w-4" />
              <span className="pb-1">Sell</span>
            </Button>
          </div>
          {lastUpdatedTime && (
            <span className="text-sm text-gray-500">
              Last updated: {lastUpdatedTime}
              <MarketStatusIndicator isOpen={isMarketOpen} />
            </span>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center mb-4 relative">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by trading code or other values..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {tableIsLoading ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 border-4 border-t-blue-500 border-b-blue-500 rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-gray-500">Loading portfolio data...</p>
          </div>
        ) : filteredAndSortedHoldings.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p className="text-lg">No holdings found.</p>
            <p className="mt-2">
              {searchQuery
                ? "Try adjusting your search query."
                : "Your portfolio holdings will appear here once you add them."}
            </p>
          </div>
        ) : (
          <Table>
            <TableCaption>Your current stock holdings</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead
                  onClick={() => handleSort("tradingCode")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Trading Code
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "tradingCode" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("currentPrice")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Current Price
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "currentPrice" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                    <RefreshButton
                      onClick={onRefreshPrices}
                      isLoading={isRefreshingPrices}
                      tooltipText="Refresh Live Prices"
                    />
                  </div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center">
                    Opening Price
                    <RefreshButton
                      onClick={onRefreshStockDetails}
                      isLoading={isRefreshingStockDetails}
                      tooltipText="Refresh Stock Details"
                    />
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("shareCount")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Share Count
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "shareCount" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("avgPrice")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Avg Price
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "avgPrice" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("totalInvestment")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Total Investment
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "totalInvestment" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="cursor-default">
                  <div className="flex items-center">Gain/Loss</div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center">52W Low</div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center">52W High</div>
                </TableHead>
                <TableHead>
                  <div className="flex items-center">P/E Ratio</div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("lastBuyDate")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Last Buy
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "lastBuyDate" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedHoldings.map((holding, index) => {
                const sellValue =
                  holding.currentPrice *
                  (1 - (commission || 0) / 100) *
                  holding.shareCount;
                const gainLoss = sellValue - holding.totalInvestment;
                const isPositive = gainLoss >= 0;
                const isApproaching52WLow =
                  holding.week52Low != null &&
                  holding.week52Low > 0 &&
                  holding.currentPrice > holding.week52Low &&
                  (holding.currentPrice - holding.week52Low) /
                    holding.week52Low <=
                    0.02;
                const isApproaching52WHigh =
                  holding.week52High != null &&
                  holding.week52High > 0 &&
                  holding.currentPrice < holding.week52High &&
                  (holding.week52High - holding.currentPrice) /
                    holding.week52High <=
                    0.02;

                const isBelow52WLow =
                  holding.week52Low != null &&
                  holding.currentPrice < holding.week52Low;

                const isAbove52WHigh =
                  holding.week52High != null &&
                  holding.currentPrice > holding.week52High;

                const week52HighClasses = isAbove52WHigh
                  ? "font-bold text-green-600"
                  : isApproaching52WHigh
                  ? "font-bold"
                  : "text-gray-500";

                const week52LowClasses = isBelow52WLow
                  ? "font-bold text-red-600"
                  : isApproaching52WLow
                  ? "font-bold"
                  : "text-gray-500";

                return (
                  <TableRow
                    key={holding.tradingCode}
                    className={
                      index % 2 === 0 ? "bg-background" : "bg-muted/50"
                    }
                  >
                    <TableCell className="font-medium">
                      {holding.tradingCode}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            {formatCurrency(holding.currentPrice)}
                            {/* {holding.isLivePrice && (
                              <span
                                className="ml-2 h-2 w-2 rounded-full bg-green-500 animate-pulse"
                                title="Live price from DSE"
                              ></span>
                            )} */}
                            {holding.changeDirection &&
                              holding.changeDirection !== "none" && (
                                <div
                                  className={`text-xs px-2 ${
                                    holding.changeDirection === "up"
                                      ? "text-green-600"
                                      : "text-red-600"
                                  }`}
                                >
                                  {holding.changeDirection === "up" ? "▲" : "▼"}
                                  {holding.changePercent !== undefined &&
                                    `${Math.abs(holding.changePercent).toFixed(
                                      2
                                    )}%`}
                                </div>
                              )}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-gray-500">
                      {holding.openingPrice
                        ? formatCurrency(holding.openingPrice)
                        : "—"}
                    </TableCell>
                    <TableCell>{holding.shareCount}</TableCell>
                    <TableCell>{formatCurrency(holding.avgPrice)}</TableCell>
                    <TableCell>
                      {formatCurrency(holding.totalInvestment)}
                    </TableCell>
                    <TableCell>
                      <span
                        className={
                          isPositive ? "text-green-600" : "text-red-600"
                        }
                      >
                        {formatGainLoss(gainLoss)}
                      </span>
                    </TableCell>
                    <TableCell className={week52LowClasses}>
                      {holding.week52Low
                        ? formatCurrency(holding.week52Low)
                        : "—"}
                    </TableCell>
                    <TableCell className={week52HighClasses}>
                      {holding.week52High
                        ? formatCurrency(holding.week52High)
                        : "—"}
                    </TableCell>
                    <TableCell className="text-gray-500">
                      {holding.peRatio ? holding.peRatio.toFixed(2) : "—"}
                    </TableCell>
                    <TableCell>{formatDate(holding.lastBuyDate)}</TableCell>
                  </TableRow>
                );
              })}
              {filteredAndSortedHoldings.length > 0 && (
                <TableRow className="font-medium bg-muted/90 border-t-4">
                  <TableCell colSpan={4} className="text-right">
                    Total:
                  </TableCell>
                  <TableCell>—</TableCell>
                  <TableCell>
                    {formatCurrency(
                      filteredAndSortedHoldings.reduce(
                        (sum, h) => sum + h.totalInvestment,
                        0
                      )
                    )}
                  </TableCell>
                  <TableCell>
                    {(() => {
                      const totalGainLoss = filteredAndSortedHoldings.reduce(
                        (sum, h) => {
                          const sellValue =
                            h.currentPrice *
                            (1 - (commission || 0) / 100) *
                            h.shareCount;
                          return sum + (sellValue - h.totalInvestment);
                        },
                        0
                      );
                      const isPositive = totalGainLoss >= 0;
                      return (
                        <span
                          className={
                            isPositive ? "text-green-600" : "text-red-600"
                          }
                        >
                          {formatGainLoss(totalGainLoss)}
                        </span>
                      );
                    })()}
                  </TableCell>
                  <TableCell colSpan={4}>—</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <Dialog open={isBuyModalOpen} onOpenChange={setIsBuyModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Buy Shares</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleBuySubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tradingCode">Trading Code</Label>
              <Select
                value={buyFormData.tradingCodeId}
                onValueChange={handleTradingCodeSelect}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a trading code" />
                </SelectTrigger>
                <SelectContent>
                  {tradingCodes.map((code) => (
                    <SelectItem key={code.id} value={code.id.toString()}>
                      {code.code} - {code.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="buyPrice">Buy Price (BDT)</Label>
              <Input
                id="buyPrice"
                name="buyPrice"
                type="number"
                step="0.01"
                min="0"
                value={buyFormData.buyPrice}
                onChange={handleBuyInputChange}
              />
              {commission !== null &&
                commission > 0 &&
                buyFormData.buyPrice && (
                  <p className="text-sm text-muted-foreground">
                    Final price per unit:{" "}
                    {formatCurrency(
                      parseFloat(buyFormData.buyPrice) * (1 + commission / 100)
                    )}
                  </p>
                )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="unitCount">Unit Count</Label>
              <Input
                id="unitCount"
                name="unitCount"
                type="number"
                min="1"
                step="1"
                value={buyFormData.unitCount}
                onChange={handleBuyInputChange}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsBuyModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isFormSubmitting}>
                {isFormSubmitting ? "Adding..." : "Add to Portfolio"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      <Dialog open={isSellModalOpen} onOpenChange={setIsSellModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sell Shares</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSellSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tradingCode">Trading Code</Label>
              <Select
                value={sellFormData.tradingCodeId}
                onValueChange={handleSellTradingCodeSelect}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a trading code" />
                </SelectTrigger>
                <SelectContent>
                  {ownedTradingCodes.map((code) => {
                    const holding = holdings.find(
                      (h) => h.tradingCode === code.code
                    );
                    return (
                      <SelectItem key={code.id} value={code.id.toString()}>
                        {code.code} - {holding?.shareCount} shares
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="sellPrice">Sell Price (BDT)</Label>
              <Input
                id="sellPrice"
                name="sellPrice"
                type="number"
                step="0.01"
                min="0"
                value={sellFormData.sellPrice}
                onChange={handleSellInputChange}
              />
              {commission !== null &&
                commission > 0 &&
                sellFormData.sellPrice && (
                  <p className="text-sm text-muted-foreground">
                    Final price per unit:{" "}
                    {formatCurrency(
                      parseFloat(sellFormData.sellPrice) *
                        (1 - commission / 100)
                    )}
                  </p>
                )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="unitCount">Unit Count</Label>
              <Input
                id="unitCount"
                name="unitCount"
                type="number"
                min="1"
                step="1"
                value={sellFormData.unitCount}
                onChange={handleSellInputChange}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsSellModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isFormSubmitting}>
                {isFormSubmitting ? "Processing..." : "Sell Shares"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isAddInvestmentModalOpen}
        onOpenChange={setIsAddInvestmentModalOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Investment Amount</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAddInvestmentSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="investmentAmount">Investment Amount (BDT)</Label>
              <Input
                id="investmentAmount"
                type="number"
                step="0.01"
                min="0"
                value={addInvestmentAmount}
                onChange={(e) => setAddInvestmentAmount(e.target.value)}
                placeholder="Enter amount to add"
              />
              <p className="text-sm text-muted-foreground">
                This amount will be added to your total investment amount.
              </p>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddInvestmentModalOpen(false)}
              >
                <span className="pb-1">Cancel</span>
              </Button>
              <Button type="submit" disabled={isFormSubmitting}>
                <span className="pb-1">
                  {isFormSubmitting ? "Adding..." : "Add Investment"}
                </span>
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isAddRealizedGainModalOpen}
        onOpenChange={setIsAddRealizedGainModalOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Realized Gain</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAddRealizedGainSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="realizedGainAmount">
                Realized Gain Amount (BDT)
              </Label>
              <Input
                id="realizedGainAmount"
                type="number"
                step="0.01"
                min="0"
                value={addRealizedGainAmount}
                onChange={(e) => setAddRealizedGainAmount(e.target.value)}
                placeholder="Enter realized gain amount"
              />
              <p className="text-sm text-muted-foreground">
                This amount will be added to your total realized gain.
              </p>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddRealizedGainModalOpen(false)}
              >
                <span className="pb-1">Cancel</span>
              </Button>
              <Button type="submit" disabled={isFormSubmitting}>
                <span className="pb-1">
                  {isFormSubmitting ? "Adding..." : "Add Realized Gain"}
                </span>
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
