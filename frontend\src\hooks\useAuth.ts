import { useSession } from "next-auth/react";

/**
 * A simplified hook to access authentication status and session data.
 * It wraps the `useSession` hook from `next-auth/react` and provides
 * a clean and centralized way to manage auth state, relying on
 * NextAuth's built-in session management and caching.
 */
export function useAuth() {
  const { data: session, status, update } = useSession();

  return {
    session,
    status,
    isAuthenticated: status === "authenticated",
    isLoading: status === "loading",
    // Expose the update function to allow for manual session refreshing.
    refreshSession: update,
  };
}
