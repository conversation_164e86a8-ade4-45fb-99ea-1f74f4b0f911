-- Create database if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dse_analyzen') THEN
        CREATE DATABASE dse_analyzen;
    END IF;
END
$$;

-- Connect to the database
\c dse_analyzen;

ALTER DATABASE dse_analyzen SET timezone TO 'UTC';
-- Create schema
CREATE SCHEMA IF NOT EXISTS dse_schema;

-- Create tables
CREATE TABLE IF NOT EXISTS dse_schema.users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create trading_codes table
CREATE TABLE IF NOT EXISTS dse_schema.trading_codes (
    id SERIAL PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL
);

-- Create portfolios table
CREATE TABLE IF NOT EXISTS dse_schema.portfolios (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES dse_schema.users(id) ON DELETE CASCADE,
    commission FLOAT DEFAULT 0.0
);

-- Create portfolio_entries table
CREATE TABLE IF NOT EXISTS dse_schema.portfolio_entries (
    id SERIAL PRIMARY KEY,
    portfolio_id INTEGER NOT NULL REFERENCES dse_schema.portfolios(id) ON DELETE CASCADE,
    trading_code_id INTEGER NOT NULL REFERENCES dse_schema.trading_codes(id) ON DELETE RESTRICT,
    buy_price FLOAT NOT NULL,
    unit_count INTEGER NOT NULL,
    buy_date DATE NOT NULL DEFAULT CURRENT_DATE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON dse_schema.users(email);
CREATE INDEX IF NOT EXISTS idx_trading_codes_code ON dse_schema.trading_codes(code);
CREATE INDEX IF NOT EXISTS idx_portfolios_user_id ON dse_schema.portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_entries_portfolio_id ON dse_schema.portfolio_entries(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_entries_trading_code_id ON dse_schema.portfolio_entries(trading_code_id);

-- Add sample data (optional) - only if the users don't already exist
DO $$
BEGIN
    -- Insert admin user if not exists
    IF NOT EXISTS (SELECT 1 FROM dse_schema.users WHERE email = '<EMAIL>') THEN
        INSERT INTO dse_schema.users (email, hashed_password, full_name, is_admin)
        VALUES ('<EMAIL>', '$2b$12$kho8u5A3SdshpfH4DY9mU.TBdkjiPF4PPeCzAqMms2hfxmd3nrHmK', 'Admin User', TRUE);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM dse_schema.users WHERE email = '<EMAIL>') THEN
        INSERT INTO dse_schema.users (email, hashed_password, full_name, is_admin)
        VALUES ('<EMAIL>', '$2b$12$kho8u5A3SdshpfH4DY9mU.TBdkjiPF4PPeCzAqMms2hfxmd3nrHmK', 'Mir Tazbinur Sharif', TRUE);
    END IF;

    -- Insert regular user if not exists
    IF NOT EXISTS (SELECT 1 FROM dse_schema.users WHERE email = '<EMAIL>') THEN
        INSERT INTO dse_schema.users (email, hashed_password, full_name, is_admin)
        VALUES ('<EMAIL>', '$2b$12$kho8u5A3SdshpfH4DY9mU.TBdkjiPF4PPeCzAqMms2hfxmd3nrHmK', 'Regular User', FALSE);
    END IF;

END
$$;
