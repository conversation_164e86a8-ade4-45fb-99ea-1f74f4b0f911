# DSE Analyzen

A fullstack application for analyzing DSE (Dhaka Stock Exchange) data.

## Project Structure

- **frontend/**: Next.js application with shadcn/ui
- **backend/**: FastAPI application with async Python
- **database/**: PostgreSQL initialization scripts

## Getting Started

### Prerequisites

- Docker and Docker Compose

### Option 1: Running Everything with Docker Compose (Recommended)

The easiest way to run the entire application is using Docker Compose:

```bash
docker-compose up -d
```

This single command will start all three services:

- Frontend: http://localhost:3000
- Backend API: http://localhost:8000 (Swagger docs at http://localhost:8000/docs)
- PostgreSQL database

All services are configured with hot reloading, so any changes you make to the code will be reflected immediately.

### Option 2: Running Services Individually

If you prefer to run services individually, you can do so as follows:

#### Prerequisites for Individual Services

- Node.js (v18+)
- Python (v3.7+)
- Docker (for PostgreSQL)

#### Running the Database

```bash
cd database
docker-compose up -d
```

This will start a PostgreSQL database with the initial schema.

#### Running the Backend

You can run the backend either directly or using a virtual environment (recommended).

**Using a virtual environment (recommended):**

```bash
# Create and activate virtual environment (only needed once)
python -m venv venv
# On Windows:
.\venv\Scripts\activate
# On macOS/Linux:
# source venv/bin/activate

# Install dependencies
pip install -r backend/requirements.txt

# Run the backend
cd backend
python -m app.main
```

**Without virtual environment:**

```bash
cd backend
pip install -r requirements.txt
# Make sure these additional dependencies are installed
pip install email-validator requests python-multipart
python -m app.main
```

The API will be available at http://localhost:8000 with Swagger documentation at http://localhost:8000/docs.

#### Running the Frontend

```bash
cd frontend
npm install
npm run dev
```

The frontend will be available at http://localhost:3000.

## Production Build

For production deployment, see the detailed [Production Build Guide](PRODUCTION_BUILD.md).

### Quick Production Build

1. **Build the frontend:**

   ```bash
   cd frontend
   npm run build
   npm run start
   ```

2. **Or use Docker Compose for production:**

   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Test the production build:**

   ```bash
   # On Windows
   test-production.bat

   # On macOS/Linux
   ./test-production.sh
   ```

### NextAuth.js v5 Configuration

The application is configured for NextAuth.js v5 with the following requirements:

- `AUTH_URL` and `AUTH_SECRET` environment variables
- `trustHost: true` configuration for production builds
- Standalone output for optimized builds

## Development

### Frontend

The frontend is built with:

- Next.js
- React
- Tailwind CSS
- shadcn/ui components
- NextAuth.js for authentication

### Backend

The backend is built with:

- FastAPI
- SQLAlchemy (async)
- PostgreSQL (via asyncpg)
- JWT authentication
- Google OAuth integration

### Database

- PostgreSQL 17
- Initial schema in `database/init.sql`

## Authentication

The application supports two authentication methods:

1. **Email/Password Login**: Traditional login with email and password
2. **Google OAuth**: Sign in with Google account

The backend provides the following authentication endpoints:

- `/api/v1/auth/login`: Traditional login with username/password
- `/api/v1/auth/login/email`: Login with email/password
- `/api/v1/auth/google`: Login with Google OAuth token

The user with email `<EMAIL>` is automatically marked as an admin user.

### User Information

After authentication, you can access the current user information at:

- `/api/v1/users/me`: Returns current user details including admin status
