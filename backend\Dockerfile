FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
# Ensure all required dependencies are installed with specific versions
RUN pip install --no-cache-dir email-validator requests python-multipart bcrypt==3.2.0

# We don't copy the source code here as we'll use a volume for development
# This allows for hot reloading

EXPOSE 8000

# Use development mode with hot reloading
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
