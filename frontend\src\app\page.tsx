"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useAuthContext } from "@/context/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Home() {
  const router = useRouter();
  const { status } = useAuthContext();

  useEffect(() => {
    if (status === "authenticated") {
      router.push("/dashboard");
    } else if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">DSE Analyzen</h1>
          <p className="text-xl text-gray-600">
            Dhaka Stock Exchange Analysis Platform
          </p>
        </div>

        <div className="w-16 h-16 border-4 border-t-blue-500 border-b-blue-500 rounded-full animate-spin mx-auto mb-8"></div>

        <p className="text-lg mb-8">Loading...</p>

        <div className="space-y-4">
          <Button
            onClick={() => router.push("/login")}
            className="w-full max-w-xs mx-auto"
          >
            Go to Login
          </Button>
        </div>
      </div>
    </div>
  );
}

