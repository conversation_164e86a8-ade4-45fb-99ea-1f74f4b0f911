from app.core.config import settings
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Create engine with execution options to set search_path to dse_schema
engine = create_async_engine(
    settings.DATABASE_URI,
    echo=False,  # Disable SQLAlchemy logging for cleaner console output
    execution_options={"schema_translate_map": {"": "dse_schema"}}
)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def get_db() -> AsyncSession:
    """Dependency for getting async DB session"""
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()
