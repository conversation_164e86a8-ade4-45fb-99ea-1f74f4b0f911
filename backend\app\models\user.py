from app.db.base_class import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


# Explicitly define the table with schema
class User(Base):
    # Override the tablename to explicitly include schema
    __tablename__ = 'users'  # Match the table name in init.sql
    __table_args__ = {'schema': 'dse_schema'}

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Define portfolios relationship with string reference to avoid circular imports
    portfolios = relationship(
        "Portfolio",
        backref="user",
        cascade="all, delete-orphan",
        lazy="selectin"
    )
