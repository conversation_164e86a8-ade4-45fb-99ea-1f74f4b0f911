import { NextResponse } from "next/server";
import { auth } from "./auth";

export default auth((req) => {
  const { nextUrl, auth } = req;
  const isLoggedIn = !!auth?.user;
  const isAuthPage = nextUrl.pathname === "/login";
  const isRegisterPage = nextUrl.pathname === "/register";
  const isDashboardPage = nextUrl.pathname.startsWith("/dashboard");

  // Redirect to login if trying to access protected page while not logged in
  if (isDashboardPage && !isLoggedIn) {
    return NextResponse.redirect(new URL("/login", nextUrl));
  }

  // Redirect to dashboard if already logged in and trying to access login or register page
  if ((isAuthPage || isRegisterPage) && isLoggedIn) {
    return NextResponse.redirect(new URL("/dashboard", nextUrl));
  }

  return NextResponse.next();
});

// Optionally, configure middleware to match specific paths
export const config = {
  matcher: ["/dashboard/:path*", "/login", "/register"],
};
